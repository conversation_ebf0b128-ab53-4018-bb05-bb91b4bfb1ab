.ticket-assignment-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

/* Header */
.agent-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-gray);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.agent-avatar {
  width: 48px;
  height: 48px;
  color: var(--primary-blue);
  background: var(--light-gray);
  border-radius: 12px;
  padding: 12px;
}

.agent-info h1 {
  color: var(--primary-blue-dark);
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 4px;
}

.agent-subtitle {
  color: var(--secondary-gray);
  font-size: 14px;
  margin: 0;
}

.agent-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.active {
  background-color: var(--success-green);
}

.status-indicator.coming-soon {
  background-color: var(--warning-yellow);
}

/* Main Grid */
.main-grid {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 24px;
}

.left-column, .right-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Card styling */
.card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-gray);
}

.card h2 {
  color: var(--primary-blue-dark);
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
}

.card h3 {
  color: var(--primary-blue-dark);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.form-subtitle {
  color: var(--secondary-gray);
  font-size: 14px;
  margin-bottom: 24px;
}

/* Form styling */
.ticket-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-weight: 500;
  color: var(--primary-blue-dark);
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 16px;
  border: 2px solid var(--border-gray);
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.form-group small {
  color: var(--secondary-gray);
  font-size: 12px;
  margin-top: 4px;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

/* Submit button */
.submit-button {
  background: var(--primary-blue);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.submit-button:hover:not(:disabled) {
  background: var(--primary-blue-dark);
  transform: translateY(-1px);
}

.submit-button:disabled {
  background: var(--secondary-gray);
  cursor: not-allowed;
  transform: none;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Sample tickets */
.sample-tickets {
  margin-top: 0;
}

.sample-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.sample-card {
  background: var(--light-gray);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--border-gray);
}

.sample-card h4 {
  color: var(--primary-blue-dark);
  font-size: 14px;
  margin-bottom: 8px;
}

.sample-card p {
  color: var(--secondary-gray);
  font-size: 12px;
  margin-bottom: 12px;
}

.sample-button {
  background: var(--primary-blue-light);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  transition: background 0.2s ease;
}

.sample-button:hover:not(:disabled) {
  background: var(--primary-blue);
}

.sample-button:disabled {
  background: var(--secondary-gray);
  cursor: not-allowed;
}

/* Assignment result */
.assignment-result {
  margin-top: 16px;
}

.success-result {
  border: 2px solid var(--success-green);
  background: rgba(16, 185, 129, 0.05);
  border-radius: 8px;
  padding: 20px;
}

.error-result {
  border: 2px solid var(--danger-red);
  background: rgba(239, 68, 68, 0.05);
  border-radius: 8px;
  padding: 20px;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.success-icon {
  color: var(--success-green);
  width: 24px;
  height: 24px;
}

.error-icon {
  color: var(--danger-red);
  width: 24px;
  height: 24px;
}

.result-header h3 {
  margin: 0;
  font-size: 18px;
}

.staff-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  border: 1px solid var(--border-gray);
}

.staff-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.staff-icon {
  width: 40px;
  height: 40px;
  background: var(--primary-blue);
  color: white;
  border-radius: 8px;
  padding: 8px;
}

.staff-info h4 {
  margin: 0;
  color: var(--primary-blue-dark);
  font-size: 16px;
}

.staff-info p {
  margin: 4px 0 0 0;
  color: var(--secondary-gray);
  font-size: 14px;
}

.staff-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--secondary-gray);
  font-size: 14px;
}

.reasoning-section {
  margin-top: 16px;
  padding: 16px;
  background: rgba(30, 64, 175, 0.05);
  border-radius: 8px;
  border-left: 4px solid var(--primary-blue);
}

.reasoning-section h4 {
  margin: 0 0 8px 0;
  color: var(--primary-blue-dark);
  font-size: 14px;
}

.reasoning-section p {
  margin: 0;
  color: var(--secondary-gray);
  font-size: 14px;
  line-height: 1.5;
}

.error-message {
  color: var(--danger-red);
  font-weight: 500;
  margin: 0 0 8px 0;
}

.error-details {
  color: var(--secondary-gray);
  margin: 0;
  font-size: 14px;
}

/* No result placeholder */
.no-result {
  text-align: center;
  padding: 40px 20px;
}

.placeholder-icon {
  color: var(--secondary-gray);
  margin-bottom: 16px;
}

.no-result p {
  color: var(--secondary-gray);
  margin-bottom: 24px;
}

.how-it-works {
  text-align: left;
  background: var(--light-gray);
  padding: 16px;
  border-radius: 8px;
}

.how-it-works h4 {
  margin: 0 0 12px 0;
  color: var(--primary-blue-dark);
  font-size: 14px;
}

.how-it-works ol {
  margin: 0;
  padding-left: 20px;
  color: var(--secondary-gray);
}

.how-it-works li {
  margin-bottom: 4px;
  font-size: 14px;
}

/* Staff workload */
.staff-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.staff-item {
  border: 1px solid var(--border-gray);
  border-radius: 8px;
  padding: 16px;
  background: var(--light-gray);
}

.staff-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.staff-basic {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-emoji {
  font-size: 16px;
}

.staff-basic h4 {
  margin: 0;
  color: var(--primary-blue-dark);
  font-size: 14px;
}

.staff-basic p {
  margin: 4px 0 0 0;
  color: var(--secondary-gray);
  font-size: 12px;
}

.workload {
  font-weight: 500;
  color: var(--primary-blue-dark);
  font-size: 14px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 3px;
}

/* Assignment history */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: var(--light-gray);
  border-radius: 6px;
  border: 1px solid var(--border-gray);
}

.history-item-detailed {
  background: var(--light-gray);
  border-radius: 8px;
  border: 1px solid var(--border-gray);
  padding: 16px;
  transition: all 0.2s ease;
}

.history-item-detailed:hover {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.history-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.history-info h4 {
  margin: 0 0 4px 0;
  color: var(--primary-blue-dark);
  font-size: 14px;
  font-weight: 600;
}

.history-info p {
  margin: 0 0 8px 0;
  color: var(--secondary-gray);
  font-size: 13px;
}

.staff-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin-top: 4px;
}

.experience-tag {
  background: var(--primary-blue);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 12px;
  text-transform: capitalize;
  font-weight: 500;
}

.module-tags {
  display: flex;
  gap: 4px;
}

.module-tag {
  background: rgba(30, 64, 175, 0.1);
  color: var(--primary-blue-dark);
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 12px;
  font-weight: 500;
}

.history-time {
  text-align: right;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-main {
  color: var(--primary-blue-dark);
  font-size: 14px;
  font-weight: 600;
}

.time-date {
  color: var(--secondary-gray);
  font-size: 11px;
}

.history-reason {
  padding: 8px 12px;
  background: rgba(30, 64, 175, 0.05);
  border-radius: 6px;
  border-left: 3px solid var(--primary-blue);
  margin-top: 8px;
}

.history-reason small {
  color: var(--secondary-gray);
  font-size: 12px;
  line-height: 1.4;
}

.history-footer {
  text-align: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-gray);
}

.history-footer p {
  margin: 0;
  color: var(--secondary-gray);
  font-size: 12px;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive design */
@media (max-width: 1024px) {
  .main-grid {
    grid-template-columns: 1fr;
  }
  
  .right-column {
    order: -1;
  }
}

@media (max-width: 768px) {
  .ticket-assignment-container {
    padding: 16px;
  }
  
  .agent-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .sample-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .card {
    padding: 16px;
  }
  
  .agent-header {
    padding: 16px;
  }
  
  .submit-button {
    padding: 14px 20px;
  }
}