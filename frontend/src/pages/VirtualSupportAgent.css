.virtual-support-container {
  max-width: 1200px;
  margin: 0 auto;
  height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

/* Header */
.agent-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.agent-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #1e40af, #3b82f6);
  color: white;
  border-radius: 12px;
  padding: 12px;
}

.agent-info h1 {
  font-size: 24px;
  color: #1f2937;
  margin: 0;
  font-weight: 700;
}

.agent-subtitle {
  color: #64748b;
  font-size: 14px;
  margin: 4px 0 0 0;
}

.agent-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #10b981;
  font-weight: 600;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.online {
  background-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

/* Expertise Panel */
.expertise-panel {
  padding: 20px 32px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
}

.expertise-panel h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 16px;
}

.expertise-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.expertise-tag {
  background: #eff6ff;
  color: #1e40af;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid #dbeafe;
}

/* Chat Container */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px 32px;
  scroll-behavior: smooth;
}

.message {
  display: flex;
  margin-bottom: 24px;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.user {
  justify-content: flex-end;
}

.message.agent {
  justify-content: flex-start;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 12px;
  flex-shrink: 0;
}

.message.user .message-avatar {
  background: #64748b;
  color: white;
  order: 1;
}

.message.agent .message-avatar {
  background: linear-gradient(135deg, #1e40af, #3b82f6);
  color: white;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.message.user .message-content {
  align-items: flex-end;
}

.message.agent .message-content {
  align-items: flex-start;
}

.message-bubble {
  background: white;
  padding: 16px 20px;
  border-radius: 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  word-wrap: break-word;
}

.message.user .message-bubble {
  background: #1e40af;
  color: white;
  border: none;
}

.message-bubble.error {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.message-text {
  line-height: 1.6;
  white-space: pre-wrap;
}

/* Markdown content styling */
.message-text h1,
.message-text h2,
.message-text h3,
.message-text h4,
.message-text h5,
.message-text h6 {
  margin: 0.5em 0 0.5em 0;
  color: inherit;
}

.message-text p {
  margin: 0.5em 0;
}

.message-text ul,
.message-text ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.message-text li {
  margin: 0.25em 0;
}

.message-text code {
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.message-text pre {
  background: rgba(0, 0, 0, 0.05);
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 0.5em 0;
}

.message-text pre code {
  background: none;
  padding: 0;
}

.message-text blockquote {
  border-left: 4px solid #e2e8f0;
  margin: 0.5em 0;
  padding-left: 1em;
  font-style: italic;
}

.message-text strong {
  font-weight: 600;
}

.message-text em {
  font-style: italic;
}

.message-sources {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e2e8f0;
}

.message-sources small {
  color: #64748b;
  font-size: 12px;
}

.message-time {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  font-size: 11px;
  color: #9ca3af;
}

/* Typing Indicator */
.message-bubble.typing {
  background: #f3f4f6;
  padding: 20px;
}

.typing-indicator {
  display: flex;
  gap: 4px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #9ca3af;
  animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Quick Questions */
.quick-questions {
  margin: 32px 0;
  padding: 24px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.quick-questions h3 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 16px;
}

.quick-questions-grid {
  display: grid;
  gap: 12px;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.quick-question-btn {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px 16px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  color: #374151;
}

.quick-question-btn:hover {
  background: #eff6ff;
  border-color: #3b82f6;
  color: #1e40af;
}

/* Input Container */
.input-container {
  padding: 24px 32px;
  background: white;
  border-top: 1px solid #e2e8f0;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  max-width: 100%;
}

.message-input {
  flex: 1;
  resize: none;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px 20px;
  font-size: 16px;
  line-height: 1.5;
  outline: none;
  transition: all 0.2s;
  font-family: inherit;
  max-height: 120px;
  overflow-y: auto;
}

.message-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.message-input:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.send-button {
  background: #1e40af;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  background: #1e3a8a;
  transform: translateY(-1px);
}

.send-button:disabled {
  background: #e2e8f0;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.input-footer {
  margin-top: 8px;
  text-align: center;
}

.input-footer small {
  color: #9ca3af;
  font-size: 12px;
}

/* Responsive */
@media (max-width: 768px) {
  .virtual-support-container {
    height: calc(100vh - 64px);
  }
  
  .agent-header,
  .expertise-panel,
  .messages-container,
  .input-container {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .agent-header {
    padding-top: 16px;
    padding-bottom: 16px;
  }
  
  .agent-info h1 {
    font-size: 20px;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .quick-questions-grid {
    grid-template-columns: 1fr;
  }
}