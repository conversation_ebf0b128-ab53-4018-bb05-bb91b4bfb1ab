import React, { useState, useEffect } from 'react';
import { 
  Users, 
  Clock, 
  AlertCircle, 
  CheckCircle, 
  Settings, 
  Send, 
  Loader,
  User,
  MapPin,
  Briefcase,
  Activity,
  TrendingUp,
  Eye
} from 'lucide-react';
import './TicketAssignmentAgent.css';

const TicketAssignmentAgent = () => {
  const [formData, setFormData] = useState({
    ticket_id: `TKT${new Date().toISOString().replace(/[^\d]/g, '').substr(0, 14)}`,
    subject: '',
    module: '',
    priority: 'Medium',
    customer_id: '',
    created_by_timezone: 'Asia/Singapore',
    issue_hash: '',
    description: ''
  });

  const [modules, setModules] = useState([]);
  const [staffWorkload, setStaffWorkload] = useState([]);
  const [assignmentHistory, setAssignmentHistory] = useState([]);
  const [assignmentResult, setAssignmentResult] = useState(null);
  const [isAssigning, setIsAssigning] = useState(false);
  const [showResult, setShowResult] = useState(false);

  const timezoneOptions = [
    'Asia/Singapore',
    'Europe/London', 
    'America/New_York',
    'Asia/Kolkata',
    'Asia/Tokyo'
  ];

  useEffect(() => {
    fetchModules();
    fetchStaffWorkload();
    fetchAssignmentHistory();
  }, []);

  const fetchModules = async () => {
    try {
      const response = await fetch('http://localhost:8000/modules');
      const data = await response.json();
      setModules(data.modules || []);
    } catch (error) {
      console.error('Error fetching modules:', error);
    }
  };

  const fetchStaffWorkload = async () => {
    try {
      const response = await fetch('http://localhost:8000/staff-workload');
      const data = await response.json();
      setStaffWorkload(data.staff_workload || []);
    } catch (error) {
      console.error('Error fetching staff workload:', error);
    }
  };

  const fetchAssignmentHistory = async () => {
    try {
      const response = await fetch('http://localhost:8000/assignment-history');
      const data = await response.json();
      setAssignmentHistory(data.assignment_history || []);
    } catch (error) {
      console.error('Error fetching assignment history:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsAssigning(true);
    setShowResult(false);

    try {
      const response = await fetch('http://localhost:8000/assign-ticket', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();
      setAssignmentResult(result);
      setShowResult(true);

      // Refresh data
      await Promise.all([
        fetchStaffWorkload(),
        fetchAssignmentHistory()
      ]);

    } catch (error) {
      console.error('Error assigning ticket:', error);
      setAssignmentResult({
        success: false,
        error: 'Failed to connect to assignment system',
        reasoning: 'Please check if the backend server is running'
      });
      setShowResult(true);
    } finally {
      setIsAssigning(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'available': return '#10b981';
      case 'overloaded': return '#ef4444';
      case 'offline': return '#6b7280';
      default: return '#f59e0b';
    }
  };

  const getStatusEmoji = (status) => {
    switch (status) {
      case 'available': return '🟢';
      case 'overloaded': return '🔴';
      case 'offline': return '⚫';
      default: return '🟡';
    }
  };

  const sampleTickets = [
    {
      name: 'Payment Issue (Returning Customer)',
      description: 'Should go to Sarah Chen (handled this before)',
      data: {
        ...formData,
        ticket_id: 'DEMO_001',
        subject: 'Payment gateway timeout error',
        module: 'Payments',
        priority: 'High',
        customer_id: 'CUST001',
        issue_hash: 'PAY_TIMEOUT_001',
        description: 'Customer experiencing timeout errors when trying to process payment'
      }
    },
    {
      name: 'Accounting Problem (High Priority)',
      description: 'Should go to Ahmed Hassan (available GL expert)',
      data: {
        ...formData,
        ticket_id: 'DEMO_002',
        subject: 'GL reconciliation failing',
        module: 'GL',
        priority: 'High',
        customer_id: 'CUST003',
        issue_hash: 'GL_RECON_URGENT',
        description: 'General ledger reconciliation process failing with database errors'
      }
    },
    {
      name: 'Document Issue (Low Priority)',
      description: 'Should go to Priya Sharma (Trade Finance expert)',
      data: {
        ...formData,
        ticket_id: 'DEMO_003',
        subject: 'Trade finance document upload issue',
        module: 'Trade Finance',
        priority: 'Low',
        customer_id: 'CUST004',
        issue_hash: 'TF_UPLOAD_001',
        description: 'Unable to upload LC documents due to file size restrictions'
      }
    }
  ];

  const handleSampleTicket = async (sampleData) => {
    setFormData(sampleData);
    setIsAssigning(true);
    setShowResult(false);

    try {
      const response = await fetch('http://localhost:8000/assign-ticket', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sampleData),
      });

      const result = await response.json();
      setAssignmentResult(result);
      setShowResult(true);

      await Promise.all([
        fetchStaffWorkload(),
        fetchAssignmentHistory()
      ]);

    } catch (error) {
      console.error('Error assigning sample ticket:', error);
      setAssignmentResult({
        success: false,
        error: 'Failed to connect to assignment system',
        reasoning: 'Please check if the backend server is running'
      });
      setShowResult(true);
    } finally {
      setIsAssigning(false);
    }
  };

  return (
    <div className="ticket-assignment-container">
      {/* Header */}
      <div className="agent-header">
        <div className="agent-info">
          <Users className="agent-avatar" />
          <div>
            <h1>Smart Ticket Assignment System</h1>
            <p className="agent-subtitle">
              Intelligent Routing • Expert Matching • Workload Balancing • 24/7 Coverage
            </p>
          </div>
        </div>
        
        <div className="agent-status">
          <div className="status-indicator active"></div>
          <span>Active</span>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="main-grid">
        {/* Left Column - Ticket Form */}
        <div className="left-column">
          <div className="card">
            <h2>Submit Support Ticket</h2>
            <p className="form-subtitle">Fill out the form below and let AI find the best agent</p>
            
            <form onSubmit={handleSubmit} className="ticket-form">
              <div className="form-group">
                <label>Ticket ID</label>
                <input
                  type="text"
                  name="ticket_id"
                  value={formData.ticket_id}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="form-group">
                <label>What's the problem?</label>
                <input
                  type="text"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  placeholder="e.g., Payment gateway timeout error"
                  required
                />
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>Module</label>
                  <select
                    name="module"
                    value={formData.module}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select Module</option>
                    {modules.map((module) => (
                      <option key={module} value={module}>
                        {module}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>Priority</label>
                  <select
                    name="priority"
                    value={formData.priority}
                    onChange={handleInputChange}
                  >
                    <option value="High">High</option>
                    <option value="Medium">Medium</option>
                    <option value="Low">Low</option>
                  </select>
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>Customer ID</label>
                  <input
                    type="text"
                    name="customer_id"
                    value={formData.customer_id}
                    onChange={handleInputChange}
                    placeholder="e.g., CUST001"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>Customer Location</label>
                  <select
                    name="created_by_timezone"
                    value={formData.created_by_timezone}
                    onChange={handleInputChange}
                  >
                    {timezoneOptions.map((tz) => (
                      <option key={tz} value={tz}>
                        {tz}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="form-group">
                <label>Issue Type (Optional)</label>
                <input
                  type="text"
                  name="issue_hash"
                  value={formData.issue_hash}
                  onChange={handleInputChange}
                  placeholder="e.g., PAY_TIMEOUT_001"
                />
                <small>Use same code for similar issues to help AI learn</small>
              </div>

              <div className="form-group">
                <label>Describe the problem</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Tell us what happened, what the customer was trying to do, any error messages, etc..."
                  rows="4"
                  required
                />
              </div>

              <button 
                type="submit" 
                className="submit-button" 
                disabled={isAssigning}
              >
                {isAssigning ? (
                  <>
                    <Loader className="spinning" />
                    Finding Best Agent...
                  </>
                ) : (
                  <>
                    <Send size={18} />
                    Find Best Agent
                  </>
                )}
              </button>
            </form>
          </div>

          {/* Sample Tickets */}
          <div className="card sample-tickets">
            <h3>Try Sample Tickets</h3>
            <p>Test the system with these example scenarios</p>
            
            <div className="sample-grid">
              {sampleTickets.map((sample, index) => (
                <div key={index} className="sample-card">
                  <h4>{sample.name}</h4>
                  <p>{sample.description}</p>
                  <button 
                    onClick={() => handleSampleTicket(sample.data)}
                    className="sample-button"
                    disabled={isAssigning}
                  >
                    Try This Example
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Assignment History */}
          {assignmentHistory.length > 0 && (
            <div className="card">
              <h3>Assignment History</h3>
              <p>Recent ticket assignments in this session</p>
              
              <div className="history-list">
                {assignmentHistory.slice(-10).reverse().map((assignment, index) => {
                  // Find staff details for this assignment
                  const staffMember = staffWorkload.find(s => s.agent_id === assignment.agent_id);
                  
                  return (
                    <div key={index} className="history-item-detailed">
                      <div className="history-main">
                        <div className="history-info">
                          <h4>Ticket {assignment.ticket_id}</h4>
                          <p>Assigned to {staffMember ? staffMember.name : assignment.agent_id}</p>
                          {staffMember && (
                            <div className="staff-tags">
                              <span className="experience-tag">{staffMember.experience_level}</span>
                              <span className="module-tags">
                                {staffMember.modules.slice(0, 2).map((module, idx) => (
                                  <span key={idx} className="module-tag">{module}</span>
                                ))}
                                {staffMember.modules.length > 2 && (
                                  <span className="module-tag">+{staffMember.modules.length - 2}</span>
                                )}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="history-time">
                          <span className="time-main">
                            {new Date(assignment.assigned_at).toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                          <span className="time-date">
                            {new Date(assignment.assigned_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      {assignment.reason && (
                        <div className="history-reason">
                          <small>{assignment.reason}</small>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
              
              {assignmentHistory.length > 10 && (
                <div className="history-footer">
                  <p>Showing latest 10 assignments ({assignmentHistory.length} total)</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Right Column - Results and Staff Info */}
        <div className="right-column">
          {/* Assignment Result */}
          <div className="card">
            <h2>Assignment Result</h2>
            
            {showResult && assignmentResult ? (
              <div className="assignment-result">
                {assignmentResult.success ? (
                  <div className="success-result">
                    <div className="result-header">
                      <CheckCircle className="success-icon" />
                      <h3>Ticket Successfully Assigned!</h3>
                    </div>
                    
                    {assignmentResult.staff_details && (
                      <div className="staff-card">
                        <div className="staff-info">
                          <User className="staff-icon" />
                          <div>
                            <h4>{assignmentResult.staff_details.name}</h4>
                            <p>{assignmentResult.staff_details.email}</p>
                          </div>
                        </div>
                        
                        <div className="staff-details">
                          <div className="detail-row">
                            <Briefcase size={16} />
                            <span>Experience: {assignmentResult.staff_details.experience_level}</span>
                          </div>
                          <div className="detail-row">
                            <Activity size={16} />
                            <span>Workload: {assignmentResult.staff_details.current_workload}/{assignmentResult.staff_details.max_capacity}</span>
                          </div>
                          <div className="detail-row">
                            <MapPin size={16} />
                            <span>Timezone: {assignmentResult.staff_details.timezone}</span>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    <div className="reasoning-section">
                      <h4>Why this agent was chosen</h4>
                      <p>{assignmentResult.reasoning}</p>
                    </div>
                  </div>
                ) : (
                  <div className="error-result">
                    <div className="result-header">
                      <AlertCircle className="error-icon" />
                      <h3>Assignment Failed</h3>
                    </div>
                    <p className="error-message">{assignmentResult.error}</p>
                    <p className="error-details">{assignmentResult.reasoning}</p>
                  </div>
                )}
              </div>
            ) : !isAssigning ? (
              <div className="no-result">
                <Settings size={48} className="placeholder-icon" />
                <p>Submit a ticket to see the assignment result</p>
                <div className="how-it-works">
                  <h4>How it works:</h4>
                  <ol>
                    <li>Fill the form with your support ticket details</li>
                    <li>AI Assignment Agent analyzes the ticket and finds the best agent</li>
                    <li>Get results with detailed reasoning</li>
                  </ol>
                </div>
              </div>
            ) : null}
          </div>

          {/* Staff Workload */}
          <div className="card">
            <h3>Live Staff Status</h3>
            <div className="staff-list">
              {staffWorkload.map((staff) => (
                <div key={staff.agent_id} className="staff-item">
                  <div className="staff-header">
                    <div className="staff-basic">
                      <span className="status-emoji">{getStatusEmoji(staff.status)}</span>
                      <div>
                        <h4>{staff.name}</h4>
                        <p>{staff.experience_level} • {staff.modules.join(', ')}</p>
                      </div>
                    </div>
                    <span className="workload">{staff.current_workload}/{staff.max_capacity}</span>
                  </div>
                  <div className="progress-bar">
                    <div 
                      className="progress-fill"
                      style={{ 
                        width: `${(staff.current_workload / staff.max_capacity) * 100}%`,
                        backgroundColor: getStatusColor(staff.status)
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Assignment History */}
          {assignmentHistory.length > 0 && (
            <div className="card">
              <h3>Assignment History</h3>
              <div className="history-list">
                {assignmentHistory.slice(-5).map((assignment, index) => (
                  <div key={index} className="history-item">
                    <div className="history-info">
                      <h4>Ticket {assignment.ticket_id}</h4>
                      <p>Assigned to {assignment.agent_id}</p>
                    </div>
                    <div className="history-time">
                      {new Date(assignment.assigned_at).toLocaleString()}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TicketAssignmentAgent;