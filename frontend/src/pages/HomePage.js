import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Bo<PERSON>, <PERSON>, ArrowRight, Shield, Clock, BookOpen } from 'lucide-react';
import './HomePage.css';

const HomePage = () => {
  return (
    <div className="home-container">
      {/* Hero Section */}
      <div className="hero-section">
        <div className="hero-content">
          <h1>Flexcube Banking Agents</h1>
          <p className="hero-description">
            Advanced AI-powered agents for Oracle Flexcube support and intelligent ticket management. 
            Streamline your banking operations with 10+ years of domain expertise.
          </p>
          <div className="hero-stats">
            <div className="stat-item">
              <Shield className="stat-icon" />
              <span>L0 Support Level</span>
            </div>
            <div className="stat-item">
              <Clock className="stat-icon" />
              <span>10+ Years Experience</span>
            </div>
            <div className="stat-item">
              <BookOpen className="stat-icon" />
              <span>All Flexcube Modules</span>
            </div>
          </div>
        </div>
      </div>

      {/* Agents Grid */}
      <div className="agents-section">
        <h2>Available Agents</h2>
        <div className="agents-grid">
          
          {/* Virtual Support Agent */}
          <div className="agent-card">
            <div className="agent-icon">
              <Bot size={48} />
            </div>
            <h3>Virtual Support Agent</h3>
            <p>
              Expert Flexcube assistant with deep knowledge of all banking modules. 
              Get instant answers to configuration questions, troubleshooting steps, 
              and best practices from our AI agent trained on 10+ years of banking expertise.
            </p>
            <div className="agent-features">
              <div className="feature-tag">Payment Processing</div>
              <div className="feature-tag">General Ledger</div>
              <div className="feature-tag">Loans Management</div>
              <div className="feature-tag">Trade Finance</div>
            </div>
            <Link to="/agents/virtual-support" className="agent-button">
              Start Chat
              <ArrowRight size={16} />
            </Link>
          </div>

          {/* Ticket Assignment Agent */}
          <div className="agent-card">
            <div className="agent-icon">
              <Users size={48} />
            </div>
            <h3>Ticket Assignment Agent</h3>
            <p>
              Intelligent ticket routing system that automatically assigns support tickets 
              to the most suitable agents based on module expertise, workload, timezone, 
              and historical performance data.
            </p>
            <div className="agent-features">
              <div className="feature-tag">Smart Routing</div>
              <div className="feature-tag">Workload Balance</div>
              <div className="feature-tag">Expertise Matching</div>
              <div className="feature-tag">24/7 Coverage</div>
            </div>
            <Link to="/agents/ticket-assignment" className="agent-button">
              View Dashboard
              <ArrowRight size={16} />
            </Link>
          </div>

        </div>
      </div>

      {/* Footer */}
      <div className="footer-section">
        <p className="powered-by">Powered by AI Planet</p>
      </div>

    </div>
  );
};

export default HomePage;