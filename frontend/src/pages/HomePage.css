.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero-section {
  text-align: center;
  padding: 60px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 20px;
  margin-bottom: 60px;
}

.hero-content h1 {
  font-size: 48px;
  color: #1f2937;
  margin: 0 0 24px 0;
  font-weight: 800;
  background: linear-gradient(135deg, #1e40af, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 20px;
  color: #64748b;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto 40px auto;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #374151;
  font-weight: 600;
}

.stat-icon {
  color: #1e40af;
  background: #eff6ff;
  padding: 8px;
  border-radius: 8px;
}

/* Agents Section */
.agents-section {
  margin-bottom: 60px;
}

.agents-section h2 {
  text-align: center;
  font-size: 36px;
  color: #1f2937;
  margin: 0 0 48px 0;
  font-weight: 700;
}

.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
}

.agent-card {
  background: white;
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 4px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.agent-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #1e40af, #3b82f6);
}

.agent-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
}

.agent-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #1e40af, #3b82f6);
  color: white;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0 24px 0;
}

.agent-card h3 {
  font-size: 24px;
  color: #1f2937;
  margin: 0 0 16px 0;
  font-weight: 700;
}

.agent-card p {
  color: #64748b;
  line-height: 1.6;
  margin: 0 0 24px 0;
}

.agent-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 0 0 32px 0;
}

.feature-tag {
  background: #eff6ff;
  color: #1e40af;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid #dbeafe;
}

.agent-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: #1e40af;
  color: white;
  padding: 16px 24px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s;
}

.agent-button:hover {
  background: #1e3a8a;
  transform: translateX(2px);
}

/* Features Section */
.features-section {
  margin-bottom: 60px;
}

.features-section h2 {
  text-align: center;
  font-size: 36px;
  color: #1f2937;
  margin: 0 0 48px 0;
  font-weight: 700;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
}

.feature-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

.feature-card h4 {
  font-size: 20px;
  color: #1f2937;
  margin: 0 0 20px 0;
  font-weight: 700;
}

.feature-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-card li {
  color: #64748b;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
  position: relative;
  padding-left: 20px;
}

.feature-card li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #10b981;
  font-weight: bold;
}

.feature-card li:last-child {
  border-bottom: none;
}

/* Footer Section */
.footer-section {
  text-align: center;
  padding: 40px 0;
  border-top: 1px solid #e2e8f0;
  margin-top: 60px;
}

.powered-by {
  color: #64748b;
  font-size: 14px;
  margin: 0;
  font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 32px;
  }
  
  .hero-description {
    font-size: 18px;
  }
  
  .hero-stats {
    gap: 20px;
  }
  
  .agents-grid,
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .agent-card,
  .feature-card {
    padding: 24px;
  }
  
  .agents-section h2,
  .features-section h2 {
    font-size: 28px;
  }
}