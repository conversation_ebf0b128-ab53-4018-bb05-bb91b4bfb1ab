import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Clock, BookOpen } from 'lucide-react';
import axios from 'axios';
import ReactMarkdown from 'react-markdown';
import './VirtualSupportAgent.css';

const VirtualSupportAgent = () => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'agent',
      content: 'Hello! I\'m your Flexcube Virtual Support Agent with over 10 years of banking industry expertise. I specialize in all Oracle Flexcube modules including Payments, GL, Loans, Trade Finance, and more. How can I assist you today?',
      timestamp: new Date(),
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [agentInfo, setAgentInfo] = useState(null);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  const API_BASE_URL = 'http://localhost:8000';

  useEffect(() => {
    // Fetch agent info on component mount
    fetchAgentInfo();
    scrollToBottom();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchAgentInfo = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/agent-info`);
      setAgentInfo(response.data);
    } catch (error) {
      console.error('Failed to fetch agent info:', error);
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await axios.post(`${API_BASE_URL}/chat`, {
        message: inputMessage,
        conversation_id: 'default'
      });

      const agentMessage = {
        id: Date.now() + 1,
        type: 'agent',
        content: response.data.response,
        sources: response.data.sources,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, agentMessage]);
    } catch (error) {
      console.error('Failed to send message:', error);
      
      const errorMessage = {
        id: Date.now() + 1,
        type: 'agent',
        content: 'I apologize, but I\'m experiencing technical difficulties. Please ensure the backend server is running on http://localhost:8000 and try again.',
        timestamp: new Date(),
        isError: true,
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      inputRef.current?.focus();
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const quickQuestions = [
    "How do I configure payment processing in Flexcube?",
    "What are the steps for GL reconciliation?",
    "How to set up loan products in the system?",
    "Trade Finance LC document requirements?",
    "How to generate customer statements?",
  ];

  const handleQuickQuestion = (question) => {
    setInputMessage(question);
    inputRef.current?.focus();
  };

  return (
    <div className="virtual-support-container">
      {/* Header */}
      <div className="agent-header">
        <div className="agent-info">
          <Bot className="agent-avatar" />
          <div>
            <h1>Flexcube Virtual Support Agent</h1>
            <p className="agent-subtitle">
              L0 Support • 10+ Years Banking Experience • Oracle Flexcube Expert
            </p>
          </div>
        </div>
        
        {agentInfo && (
          <div className="agent-status">
            <div className="status-indicator online"></div>
            <span>Online</span>
          </div>
        )}
      </div>


      {/* Chat Container */}
      <div className="chat-container">
        <div className="messages-container">
          {messages.map((message) => (
            <div key={message.id} className={`message ${message.type}`}>
              <div className="message-avatar">
                {message.type === 'user' ? <User size={20} /> : <Bot size={20} />}
              </div>
              <div className="message-content">
                <div className={`message-bubble ${message.isError ? 'error' : ''}`}>
                  <div className="message-text">
                    {message.type === 'agent' ? (
                      <ReactMarkdown>{message.content}</ReactMarkdown>
                    ) : (
                      message.content
                    )}
                  </div>
                  {message.sources && message.sources.length > 0 && (
                    <div className="message-sources">
                      <small>Sources: {message.sources.join(', ')}</small>
                    </div>
                  )}
                </div>
                <div className="message-time">
                  <Clock size={12} />
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
            </div>
          ))}
          
          {isLoading && (
            <div className="message agent">
              <div className="message-avatar">
                <Bot size={20} />
              </div>
              <div className="message-content">
                <div className="message-bubble typing">
                  <div className="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Quick Questions */}
        {messages.length === 1 && (
          <div className="quick-questions">
            <h3>Quick Questions</h3>
            <div className="quick-questions-grid">
              {quickQuestions.map((question, index) => (
                <button
                  key={index}
                  className="quick-question-btn"
                  onClick={() => handleQuickQuestion(question)}
                >
                  {question}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="input-container">
          <div className="input-wrapper">
            <textarea
              ref={inputRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about Flexcube modules, configurations, or banking operations..."
              className="message-input"
              rows="1"
              disabled={isLoading}
            />
            <button
              onClick={sendMessage}
              disabled={!inputMessage.trim() || isLoading}
              className="send-button"
            >
              <Send size={20} />
            </button>
          </div>
          <div className="input-footer">
            <small>Press Enter to send • Shift+Enter for new line</small>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VirtualSupportAgent;