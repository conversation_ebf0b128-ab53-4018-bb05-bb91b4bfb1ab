import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import HomePage from './pages/HomePage';
import VirtualSupportAgent from './pages/VirtualSupportAgent';
import TicketAssignmentAgent from './pages/TicketAssignmentAgent';
import './App.css';

function App() {
  return (
    <div className="App">
      <Router>
        <Navbar />
        <main className="main-content">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/agents/virtual-support" element={<VirtualSupportAgent />} />
            <Route path="/agents/ticket-assignment" element={<TicketAssignmentAgent />} />
          </Routes>
        </main>
      </Router>
    </div>
  );
}

export default App;