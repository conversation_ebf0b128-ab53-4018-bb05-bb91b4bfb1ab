import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronDown, Bot, Users, Home } from 'lucide-react';
import './Navbar.css';

const Navbar = () => {
  const [isAgentsDropdownOpen, setIsAgentsDropdownOpen] = useState(false);
  const location = useLocation();

  const toggleAgentsDropdown = () => {
    setIsAgentsDropdownOpen(!isAgentsDropdownOpen);
  };

  const closeDropdown = () => {
    setIsAgentsDropdownOpen(false);
  };

  return (
    <nav className="navbar">
      <div className="navbar-container">
        {/* Logo/Brand */}
        <div className="navbar-brand">
          <Link to="/" className="brand-link">
            <Bot className="brand-icon" />
            <span>Flexcube Agents</span>
          </Link>
        </div>

        {/* Navigation Links */}
        <div className="navbar-links">
          <Link 
            to="/" 
            className={`nav-link ${location.pathname === '/' ? 'active' : ''}`}
          >
            <Home size={18} />
            Home
          </Link>

          {/* Agents Dropdown */}
          <div className="dropdown">
            <button 
              className={`dropdown-toggle ${location.pathname.startsWith('/agents') ? 'active' : ''}`}
              onClick={toggleAgentsDropdown}
            >
              <Users size={18} />
              Agents
              <ChevronDown 
                size={16} 
                className={`chevron ${isAgentsDropdownOpen ? 'open' : ''}`} 
              />
            </button>

            {isAgentsDropdownOpen && (
              <div className="dropdown-menu">
                <Link 
                  to="/agents/virtual-support" 
                  className={`dropdown-item ${location.pathname === '/agents/virtual-support' ? 'active' : ''}`}
                  onClick={closeDropdown}
                >
                  <Bot size={16} />
                  Virtual Support
                  <span className="agent-description">Flexcube Expert Assistant</span>
                </Link>
                <Link 
                  to="/agents/ticket-assignment" 
                  className={`dropdown-item ${location.pathname === '/agents/ticket-assignment' ? 'active' : ''}`}
                  onClick={closeDropdown}
                >
                  <Users size={16} />
                  Ticket Assignment
                  <span className="agent-description">Intelligent Ticket Routing</span>
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* User Info */}
        <div className="navbar-user">
          <span className="user-role">L0 Support</span>
        </div>
      </div>

      {/* Overlay to close dropdown when clicking outside */}
      {isAgentsDropdownOpen && (
        <div className="dropdown-overlay" onClick={closeDropdown}></div>
      )}
    </nav>
  );
};

export default Navbar;