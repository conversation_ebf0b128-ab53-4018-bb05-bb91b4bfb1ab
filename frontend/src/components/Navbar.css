.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 64px;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  box-shadow: 0 2px 10px rgba(30, 64, 175, 0.2);
  z-index: 1000;
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Brand */
.navbar-brand {
  display: flex;
  align-items: center;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  color: white;
  font-size: 20px;
  font-weight: 700;
  transition: opacity 0.2s;
}

.brand-link:hover {
  opacity: 0.9;
}

.brand-icon {
  width: 28px;
  height: 28px;
}

/* Navigation Links */
.navbar-links {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-link.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
}

/* Dropdown */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.dropdown-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.dropdown-toggle.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
}

.chevron {
  transition: transform 0.2s;
}

.chevron.open {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  min-width: 280px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  animation: dropdownOpen 0.2s ease-out;
}

@keyframes dropdownOpen {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  color: #1f2937;
  text-decoration: none;
  transition: all 0.2s;
  border-radius: 12px;
}

.dropdown-item:hover {
  background-color: #f8fafc;
  color: #1e40af;
}

.dropdown-item.active {
  background-color: #eff6ff;
  color: #1e40af;
}

.agent-description {
  font-size: 12px;
  color: #64748b;
  margin-left: auto;
  font-weight: 400;
}

.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

/* User Info */
.navbar-user {
  display: flex;
  align-items: center;
}

.user-role {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 600;
  padding: 6px 12px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
}

/* Responsive */
@media (max-width: 768px) {
  .navbar-container {
    padding: 0 16px;
  }
  
  .brand-link {
    font-size: 18px;
  }
  
  .navbar-links {
    gap: 4px;
  }
  
  .nav-link, .dropdown-toggle {
    padding: 8px 12px;
    font-size: 14px;
  }
  
  .dropdown-menu {
    min-width: 240px;
    left: -80px;
  }
}