* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
}

.App {
  min-height: 100vh;
}

.main-content {
  margin-top: 64px; /* Account for fixed navbar */
  padding: 20px;
}

/* Banking theme colors */
:root {
  --primary-blue: #1e40af;
  --primary-blue-light: #3b82f6;
  --primary-blue-dark: #1e3a8a;
  --secondary-gray: #64748b;
  --light-gray: #f1f5f9;
  --border-gray: #e2e8f0;
  --success-green: #10b981;
  --warning-yellow: #f59e0b;
  --danger-red: #ef4444;
}

/* Component styles */
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 24px;
  border: 1px solid var(--border-gray);
}

.button-primary {
  background-color: var(--primary-blue);
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
}

.button-primary:hover {
  background-color: var(--primary-blue-dark);
}

.button-secondary {
  background-color: white;
  color: var(--primary-blue);
  padding: 12px 24px;
  border: 2px solid var(--primary-blue);
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.button-secondary:hover {
  background-color: var(--primary-blue);
  color: white;
}